import React from 'react';
import { DownloadProgress } from '../types';

interface ProgressBarProps {
  progress: DownloadProgress;
}

export function ProgressBar({ progress }: ProgressBarProps) {
  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  return (
    <div className="w-full space-y-2">
      {/* Progress bar */}
      <div className="w-full bg-gray-700 rounded-full h-2 overflow-hidden">
        <div 
          className="bg-blue-500 h-2 rounded-full transition-all duration-300 ease-out"
          style={{ width: `${Math.min(100, Math.max(0, progress.percentage))}%` }}
        />
      </div>
      
      {/* Progress text */}
      <div className="flex justify-between items-center text-xs text-gray-400">
        <span>{progress.phase}</span>
        <span>{Math.round(progress.percentage)}%</span>
      </div>
      
      {/* File info */}
      {progress.currentFile && (
        <div className="text-xs text-gray-500 truncate">
          {progress.currentFile}
        </div>
      )}
      
      {/* Download stats */}
      {progress.totalBytes > 0 && (
        <div className="flex justify-between items-center text-xs text-gray-500">
          <span>
            {formatBytes(progress.downloadedBytes)} / {formatBytes(progress.totalBytes)}
          </span>
          {progress.downloadSpeed > 0 && (
            <span>
              {formatBytes(progress.downloadSpeed)}/s
            </span>
          )}
        </div>
      )}
    </div>
  );
}
