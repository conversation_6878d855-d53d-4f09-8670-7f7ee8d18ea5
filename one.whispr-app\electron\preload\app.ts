import { ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * Main application IPC methods (settings, backend communication, etc.)
 */
export const appAPI = {
  // === DATABASE IPC (for settings, etc.) ===
  
  // Settings database methods
  getSettings: () => ipcRenderer.invoke('settings:get'),
  updateSettings: (updates: any) => ipcRenderer.invoke('settings:update', updates),
  
  // === BACKEND COMMUNICATION ===
  
  // Backend communication
  sendCommand: (type: string, args?: any) => ipcRenderer.invoke('send-command', type, args),
  getBackendStatus: () => ipcRenderer.invoke('python-get-status'),
  reconnectBackend: () => ipcRenderer.invoke('python-reconnect'),
  
  // Backend WebSocket message listener
  onBackendMessage: (callback: (message: any) => void) => {
    const subscription = (_event: any, message: any) => callback(message);
    ipcRenderer.on('python-ws-message', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-message', subscription);
    };
  },
  
  // Backend connection status listeners
  onBackendConnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connected', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-connected', subscription);
    };
  },

  onBackendConnectionFailed: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connection-failed', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-connection-failed', subscription);
    };
  },

  onBackendDisconnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-disconnected', subscription);

    return () => {
      ipcRenderer.removeListener('python-ws-disconnected', subscription);
    };
  }
};
