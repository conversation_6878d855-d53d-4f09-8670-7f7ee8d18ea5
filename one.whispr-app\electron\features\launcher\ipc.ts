import { ipc<PERSON>ain } from 'electron';
import { LauncherManager } from './launcher-manager';

/**
 * Setup IPC handlers for launcher operations
 */
export function setupLauncherIpc(launcherManager: LauncherManager): void {
  console.log('[LAUNCHER] Setting up IPC handlers...');

  // Remove existing handlers first to prevent duplicates
  ipcMain.removeHandler('launcher:check-updates');
  ipcMain.removeHandler('launcher:download-updates');
  ipcMain.removeHandler('launcher:extract-updates');
  ipcMain.removeHandler('launcher:launch-main-app');
  ipcMain.removeHandler('launcher:exit');

  // Check for updates
  ipcMain.handle('launcher:check-updates', async () => {
    try {
      return await launcherManager.checkForUpdates();
    } catch (error) {
      console.error('[LAUNCHER] Error checking for updates:', error);
      throw error;
    }
  });

  // Download updates
  ipcMain.handle('launcher:download-updates', async () => {
    try {
      return await launcherManager.downloadUpdates();
    } catch (error) {
      console.error('[LAUNCHER] Error downloading updates:', error);
      throw error;
    }
  });

  // Extract updates
  ipcMain.handle('launcher:extract-updates', async () => {
    try {
      return await launcherManager.extractUpdates();
    } catch (error) {
      console.error('[LAUNCHER] Error extracting updates:', error);
      throw error;
    }
  });

  // Launch main app (for Microsoft builds, this transitions to main app)
  ipcMain.handle('launcher:launch-main-app', async () => {
    try {
      return await launcherManager.launchMainApp();
    } catch (error) {
      console.error('[LAUNCHER] Error launching main app:', error);
      throw error;
    }
  });

  // Exit launcher
  ipcMain.handle('launcher:exit', async () => {
    try {
      return await launcherManager.exitLauncher();
    } catch (error) {
      console.error('[LAUNCHER] Error exiting launcher:', error);
      throw error;
    }
  });

  console.log('[LAUNCHER] IPC handlers registered');
}
