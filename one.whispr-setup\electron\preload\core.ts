import { ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * Core IPC methods - basic communication with main process
 */
export const coreAPI = {
  // Send messages to main process
  send: (channel: string, ...args: any[]) => {
    ipcRenderer.send(channel, ...args);
  },
  
  // Invoke methods in main process and return result
  invoke: (channel: string, ...args: any[]): Promise<any> => {
    return ipcRenderer.invoke(channel, ...args);
  },
  
  // Listen for events from main process
  on: (channel: string, listener: (...args: any[]) => void) => {
    ipcRenderer.on(channel, (_event, ...args) => listener(...args));
    
    // Return a function to remove the listener
    return () => {
      ipcRenderer.removeListener(channel, (_event, ...args) => listener(...args));
    };
  },
  
  // Remove all listeners for a specific channel
  removeAllListeners: (channel: string) => {
    ipcRenderer.removeAllListeners(channel);
  },
  
  // Listen for one message and then remove listener
  once: (channel: string, listener: (...args: any[]) => void) => {
    ipcRenderer.once(channel, (_event, ...args) => listener(...args));
  }
};
