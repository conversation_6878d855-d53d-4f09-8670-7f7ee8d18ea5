import { ipc<PERSON><PERSON><PERSON> } from 'electron';

// Auth-related types
export interface AuthCallbackData {
  token: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
}

export interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatarUrl?: string;
  provider: 'email' | 'google' | 'twitter';
  providerId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserSession {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt: string;
  scopes?: string[];
  deviceInfo?: {
    platform: string;
    userAgent?: string;
    deviceId: string;
  };
  isActive: boolean;
  lastAccessedAt: string;
  createdAt: string;
}

export interface AuthSettings {
  id: string;
  userId: string;
  autoLogin: boolean;
  rememberDevice: boolean;
  sessionTimeout: number;
  twoFactorEnabled: boolean;
  lastPasswordChange?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthStatus {
  isAuthenticated: boolean;
  user?: User;
  session?: UserSession;
}

export interface AuthSuccessData {
  user: User;
  session: UserSession;
  token: string;
}

export interface AuthErrorData {
  error: string;
}

export interface AuthStateChangeData {
  isAuthenticated: boolean;
  user?: User;
}

/**
 * Authentication-related IPC methods
 */
export const authAPI = {
  // Authentication flow methods
  openExternalUrl: (url: string) => ipcRenderer.invoke('open-external-url', url),
  getAuthUrls: () => ipcRenderer.invoke('get-auth-urls'),
  
  // Get current authentication status
  getAuthStatus: () => ipcRenderer.invoke('auth:getStatus') as Promise<AuthStatus>,
  
  // Get current user's access token
  getAccessToken: () => ipcRenderer.invoke('auth:getAccessToken') as Promise<string | null>,
  
  // Logout current user
  logout: () => ipcRenderer.invoke('auth:logout') as Promise<boolean>,
  
  // Update user activity (extend session)
  updateActivity: () => ipcRenderer.invoke('auth:updateActivity') as Promise<boolean>,
  
  // Get current user
  getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser') as Promise<User | null>,
  
  // Get user settings
  getUserSettings: (userId?: string) => ipcRenderer.invoke('auth:getUserSettings', userId) as Promise<AuthSettings | null>,
  
  // Process authentication callback (for manual triggering)
  processAuthCallback: (callbackData: AuthCallbackData) => ipcRenderer.send('auth:processCallback', callbackData),
  
  // Event listeners
  onAuthCallback: (callback: (data: AuthCallbackData) => void) => {
    const subscription = (_event: any, data: AuthCallbackData) => callback(data);
    ipcRenderer.on('auth-callback', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-callback', subscription);
    };
  },

  onAuthSuccess: (callback: (data: AuthSuccessData) => void) => {
    const subscription = (_event: any, data: AuthSuccessData) => callback(data);
    ipcRenderer.on('auth-success', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-success', subscription);
    };
  },

  onAuthError: (callback: (data: AuthErrorData) => void) => {
    const subscription = (_event: any, data: AuthErrorData) => callback(data);
    ipcRenderer.on('auth-error', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-error', subscription);
    };
  },

  onAuthStateChange: (callback: (data: AuthStateChangeData) => void) => {
    const subscription = (_event: any, data: AuthStateChangeData) => callback(data);
    ipcRenderer.on('auth-state-change', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-state-change', subscription);
    };
  }
};
