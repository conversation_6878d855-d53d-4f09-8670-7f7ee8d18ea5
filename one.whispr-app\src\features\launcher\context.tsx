import React, { createContext, useContext, useReducer, useCallback, ReactNode } from 'react';
import { LauncherState, LauncherContextType, DownloadProgress } from './types';

// Initial state
const initialState: LauncherState = {
  currentPhase: 'initializing',
  isReady: false,
  needsUpdate: false,
  error: undefined,
  mainAppError: undefined,
  progress: undefined,
  updateType: undefined
};

// Action types
type LauncherAction =
  | { type: 'SET_PHASE'; payload: LauncherState['currentPhase'] }
  | { type: 'SET_PROGRESS'; payload: DownloadProgress }
  | { type: 'SET_ERROR'; payload: string }
  | { type: 'SET_MAIN_APP_ERROR'; payload: string }
  | { type: 'SET_READY'; payload: boolean }
  | { type: 'SET_NEEDS_UPDATE'; payload: { needsUpdate: boolean; updateType?: 'scripts' | 'runtime' | 'both' } }
  | { type: 'CLEAR_ERROR' }
  | { type: 'RESET' };

// Reducer
function launcherReducer(state: LauncherState, action: LauncherAction): LauncherState {
  switch (action.type) {
    case 'SET_PHASE':
      return { ...state, currentPhase: action.payload };
    case 'SET_PROGRESS':
      return { ...state, progress: action.payload };
    case 'SET_ERROR':
      return { ...state, error: action.payload, currentPhase: 'error' };
    case 'SET_MAIN_APP_ERROR':
      return { ...state, mainAppError: action.payload };
    case 'SET_READY':
      return { ...state, isReady: action.payload };
    case 'SET_NEEDS_UPDATE':
      return { 
        ...state, 
        needsUpdate: action.payload.needsUpdate,
        updateType: action.payload.updateType
      };
    case 'CLEAR_ERROR':
      return { ...state, error: undefined, mainAppError: undefined };
    case 'RESET':
      return initialState;
    default:
      return state;
  }
}

// Context
const LauncherContext = createContext<LauncherContextType | undefined>(undefined);

// Provider component
interface LauncherProviderProps {
  children: ReactNode;
}

export function LauncherProvider({ children }: LauncherProviderProps) {
  const [state, dispatch] = useReducer(launcherReducer, initialState);

  // Set up event listeners for progress updates
  React.useEffect(() => {
    if (!window.electron) return;

    const handleProgress = (progress: DownloadProgress) => {
      dispatch({ type: 'SET_PROGRESS', payload: progress });
    };

    const handleMainAppError = (error: { error: string; details?: string }) => {
      dispatch({
        type: 'SET_MAIN_APP_ERROR',
        payload: error.details || error.error
      });
    };

    // Listen for progress updates
    const cleanupProgress = window.electron.onLauncherProgress(handleProgress);
    const cleanupError = window.electron.onLauncherMainAppError(handleMainAppError);

    return () => {
      cleanupProgress();
      cleanupError();
    };
  }, []);

  const checkForUpdates = useCallback(async (): Promise<boolean> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      dispatch({ type: 'SET_PHASE', payload: 'checking' });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await window.electron.checkLauncherUpdates();

      if (result.hasUpdate) {
        dispatch({
          type: 'SET_NEEDS_UPDATE',
          payload: {
            needsUpdate: true,
            updateType: result.updateType
          }
        });
      } else {
        dispatch({
          type: 'SET_NEEDS_UPDATE',
          payload: { needsUpdate: false }
        });
      }

      return result.hasUpdate;
    } catch (error) {
      console.error('Failed to check for updates:', error);
      dispatch({
        type: 'SET_ERROR',
        payload: error instanceof Error ? error.message : String(error)
      });
      return false;
    }
  }, []);

  const downloadUpdates = useCallback(async (): Promise<boolean> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      dispatch({ type: 'SET_PHASE', payload: 'downloading' });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await window.electron.downloadLauncherUpdates();
      return result;
    } catch (error) {
      console.error('Failed to download updates:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }, []);

  const extractUpdates = useCallback(async (): Promise<boolean> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      dispatch({ type: 'SET_PHASE', payload: 'extracting' });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await window.electron.extractLauncherUpdates();
      return result;
    } catch (error) {
      console.error('Failed to extract updates:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }, []);

  const launchMainApp = useCallback(async (): Promise<boolean> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      dispatch({ type: 'SET_PHASE', payload: 'starting' });
      dispatch({ type: 'CLEAR_ERROR' });

      const result = await window.electron.launchMainApp();
      
      if (result) {
        dispatch({ type: 'SET_PHASE', payload: 'waiting' });
      }

      return result;
    } catch (error) {
      console.error('Failed to launch main app:', error);
      dispatch({ 
        type: 'SET_ERROR', 
        payload: error instanceof Error ? error.message : String(error) 
      });
      return false;
    }
  }, []);

  const exitLauncher = useCallback(async (): Promise<void> => {
    try {
      if (!window.electron) {
        throw new Error('Electron API not available');
      }

      await window.electron.exitLauncher();
    } catch (error) {
      console.error('Failed to exit launcher:', error);
    }
  }, []);

  const contextValue: LauncherContextType = {
    state,
    checkForUpdates,
    downloadUpdates,
    extractUpdates,
    launchMainApp,
    exitLauncher
  };

  return (
    <LauncherContext.Provider value={contextValue}>
      {children}
    </LauncherContext.Provider>
  );
}

// Hook to use the launcher context
export function useLauncher(): LauncherContextType {
  const context = useContext(LauncherContext);
  if (context === undefined) {
    throw new Error('useLauncher must be used within a LauncherProvider');
  }
  return context;
}
