interface ErrorOverlayProps {
  error: string;
}

export function ErrorOverlay({ error }: ErrorOverlayProps) {
  return (
    <div className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-red-900/90 border border-red-700 rounded-lg p-6 max-w-md mx-4 text-center">
        <div className="text-red-400 text-lg font-semibold mb-2">
          Error
        </div>
        <div className="text-red-200 text-sm leading-relaxed">
          {error}
        </div>
        <div className="mt-4 text-xs text-red-300">
          Please try restarting the application
        </div>
      </div>
    </div>
  );
}
