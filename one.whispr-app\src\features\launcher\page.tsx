import React, { useEffect } from 'react';
import { useLauncher } from './context';
import { ProgressBar } from './components/ProgressBar';
import { ErrorOverlay } from './components/ErrorOverlay';

export function LauncherPage() {
  const { state, checkForUpdates, downloadUpdates, extractUpdates, launchMainApp } = useLauncher();

  useEffect(() => {
    const startLauncherSequence = async () => {
      try {
        console.log('[LAUNCHER] Starting launcher sequence...');

        // Get environment variables from main process
        if (!window.electron) {
          console.error('[LAUNCHER] Electron API not available');
          throw new Error('Electron API not available');
        }

        console.log('[LAUNCHER] Getting environment variables...');
        const env = await window.electron.getLauncherEnv();
        const isDev = env.NODE_ENV === 'development';
        const isMicrosoft = env.IS_MICROSOFT === 'true';
        console.log('[LAUNCHER] Environment check - isDev:', isDev, 'isMicrosoft:', isMicrosoft);

        if (isDev && !isMicrosoft) {
          // In dev mode without Microsoft flag, launch directly
          console.log('[LAUNCHER] Development mode - launching directly');
          await launchMainApp();
          return;
        }

        // Microsoft mode or production - check for readiness first
        console.log('[LAUNCHER] Microsoft/production mode - checking launch readiness');

        const readiness = await window.electron.checkLauncherReady();
        console.log('[LAUNCHER] Readiness result:', readiness);

        const needsWork = !readiness.allReady;
        console.log('[LAUNCHER] Needs work:', needsWork);

        if (needsWork) {
          // Download and extract updates
          console.log('[LAUNCHER] Starting downloads and extraction...');

          const downloadSuccess = await downloadUpdates();
          if (downloadSuccess) {
            console.log('[LAUNCHER] Download complete, extracting...');
            const extractSuccess = await extractUpdates();

            if (!extractSuccess) {
              throw new Error('Failed to extract updates');
            }
          } else {
            throw new Error('Failed to download updates');
          }
        }

        // Launch the main app
        console.log('[LAUNCHER] Launching main app...');
        await launchMainApp();

      } catch (error) {
        console.error('[LAUNCHER] Error in launcher sequence:', error);
      }
    };

    startLauncherSequence();
  }, [checkForUpdates, downloadUpdates, extractUpdates, launchMainApp]);

  const getPhaseText = () => {
    switch (state.currentPhase) {
      case 'initializing':
        return 'Initializing...';
      case 'checking':
        return 'Checking for updates...';
      case 'downloading':
        return 'Downloading updates...';
      case 'extracting':
        return 'Extracting files...';
      case 'starting':
        return 'Starting One Whispr...';
      case 'waiting':
        return 'Loading AI models...';
      case 'error':
        return 'Error occurred';
      case 'complete':
        return 'Complete';
      default:
        return 'Please wait...';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-8">
      <div className="relative w-full max-w-md">
        {/* Main content */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 text-center">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-2xl font-bold">W</span>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">One Whispr</h1>
            <p className="text-gray-400 text-sm">AI-powered transcription</p>
          </div>

          {/* Status */}
          <div className="mb-6">
            <p className="text-white text-lg font-medium mb-2">
              {getPhaseText()}
            </p>
            
            {state.updateType && (
              <p className="text-gray-400 text-sm">
                Updating {state.updateType === 'both' ? 'runtime and scripts' : state.updateType}
              </p>
            )}
          </div>

          {/* Progress */}
          {state.progress && (
            <div className="mb-6">
              <ProgressBar progress={state.progress} />
            </div>
          )}

          {/* Loading indicator for phases without progress */}
          {!state.progress && state.currentPhase !== 'error' && (
            <div className="mb-6">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            </div>
          )}

          {/* Phase-specific messages */}
          {state.currentPhase === 'waiting' && (
            <div className="text-gray-400 text-sm">
              This may take a moment on first launch...
            </div>
          )}
        </div>

        {/* Error overlay */}
        {state.error && <ErrorOverlay error={state.error} />}
        {state.mainAppError && <ErrorOverlay error={state.mainAppError} />}
      </div>
    </div>
  );
}
