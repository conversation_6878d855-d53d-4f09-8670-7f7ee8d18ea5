import React, { useEffect } from 'react';
import { useLauncher } from './context';
import { ProgressBar } from './components/ProgressBar';
import { ErrorOverlay } from './components/ErrorOverlay';

export function LauncherPage() {
  const { state, checkForUpdates, downloadUpdates, extractUpdates, launchMainApp } = useLauncher();

  useEffect(() => {
    const startLauncherSequence = async () => {
      try {
        console.log('[LAUNCHER] Starting launcher sequence...');
        
        // Check for updates first
        const hasUpdates = await checkForUpdates();
        
        if (hasUpdates) {
          console.log('[LAUNCHER] Updates found, downloading...');
          const downloadSuccess = await downloadUpdates();
          
          if (downloadSuccess) {
            console.log('[LAUNCHER] Download complete, extracting...');
            const extractSuccess = await extractUpdates();
            
            if (!extractSuccess) {
              throw new Error('Failed to extract updates');
            }
          } else {
            throw new Error('Failed to download updates');
          }
        }
        
        // Launch the main app
        console.log('[LAUNCHER] Launching main app...');
        await launchMainApp();
        
      } catch (error) {
        console.error('[LAUNCHER] Error in launcher sequence:', error);
      }
    };

    startLauncherSequence();
  }, [checkForUpdates, downloadUpdates, extractUpdates, launchMainApp]);

  const getPhaseText = () => {
    switch (state.currentPhase) {
      case 'initializing':
        return 'Initializing...';
      case 'checking':
        return 'Checking for updates...';
      case 'downloading':
        return 'Downloading updates...';
      case 'extracting':
        return 'Extracting files...';
      case 'starting':
        return 'Starting One Whispr...';
      case 'waiting':
        return 'Loading AI models...';
      case 'error':
        return 'Error occurred';
      case 'complete':
        return 'Complete';
      default:
        return 'Please wait...';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 flex items-center justify-center p-8">
      <div className="relative w-full max-w-md">
        {/* Main content */}
        <div className="bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-xl p-8 text-center">
          {/* Logo */}
          <div className="mb-8">
            <div className="w-16 h-16 mx-auto mb-4 bg-blue-500 rounded-full flex items-center justify-center">
              <span className="text-white text-2xl font-bold">W</span>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">One Whispr</h1>
            <p className="text-gray-400 text-sm">AI-powered transcription</p>
          </div>

          {/* Status */}
          <div className="mb-6">
            <p className="text-white text-lg font-medium mb-2">
              {getPhaseText()}
            </p>
            
            {state.updateType && (
              <p className="text-gray-400 text-sm">
                Updating {state.updateType === 'both' ? 'runtime and scripts' : state.updateType}
              </p>
            )}
          </div>

          {/* Progress */}
          {state.progress && (
            <div className="mb-6">
              <ProgressBar progress={state.progress} />
            </div>
          )}

          {/* Loading indicator for phases without progress */}
          {!state.progress && state.currentPhase !== 'error' && (
            <div className="mb-6">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            </div>
          )}

          {/* Phase-specific messages */}
          {state.currentPhase === 'waiting' && (
            <div className="text-gray-400 text-sm">
              This may take a moment on first launch...
            </div>
          )}
        </div>

        {/* Error overlay */}
        {state.error && <ErrorOverlay error={state.error} />}
        {state.mainAppError && <ErrorOverlay error={state.mainAppError} />}
      </div>
    </div>
  );
}
