import axios from 'axios';
import * as fs from 'fs-extra';
import * as path from 'path';

interface VersionInfo {
  version: string;
  releaseDate: string;
  downloadUrl: string;
  checksum?: string;
  isRollback?: boolean;
}

interface UpdateInfo {
  hasUpdate: boolean;
  updateType: 'scripts' | 'runtime' | 'both';
  scriptsVersion?: string;
  runtimeVersion?: string;
  scriptsSize?: number;
  runtimeSize?: number;
}

/**
 * Manages version checking for Microsoft Store builds
 * Only checks for scripts updates, not runtime (handled by Microsoft Store)
 */
export class VersionManager {
  private backendPath: string;
  private readonly UPDATE_URLS = {
    scripts: 'https://whispr.one/updates/backend-scripts/scripts-version.json',
    runtime: 'https://whispr.one/updates/backend-runtime/runtime-version.json'
  };

  constructor(backendPath: string) {
    this.backendPath = backendPath;
  }

  /**
   * Check for updates (Microsoft builds only check scripts)
   */
  async checkForUpdates(): Promise<UpdateInfo> {
    try {
      console.log('[VERSION] Checking for updates...');
      
      // For Microsoft builds, only check scripts updates
      const scriptsUpdate = await this.checkScriptsUpdate();
      
      return {
        hasUpdate: scriptsUpdate.needed,
        updateType: 'scripts',
        scriptsVersion: scriptsUpdate.version,
        scriptsSize: scriptsUpdate.size
      };
    } catch (error) {
      console.error('[VERSION] Error checking for updates:', error);
      return {
        hasUpdate: false,
        updateType: 'scripts'
      };
    }
  }

  /**
   * Check if scripts update is needed
   */
  private async checkScriptsUpdate(): Promise<{ needed: boolean; version?: string; size?: number }> {
    try {
      // Fetch server version
      const serverVersion = await this.fetchVersionInfo('scripts');
      if (!serverVersion) {
        return { needed: false };
      }

      // Check local version
      const localVersionPath = path.join(this.backendPath, 'scripts', 'scripts-version.json');
      
      if (!fs.existsSync(localVersionPath)) {
        console.log('[VERSION] No local scripts version found - update needed');
        return { 
          needed: true, 
          version: serverVersion.version,
          size: await this.getFileSize(serverVersion.downloadUrl)
        };
      }

      const localVersion = await fs.readJson(localVersionPath);
      
      // Compare release dates
      const serverDate = new Date(serverVersion.releaseDate);
      const localDate = new Date(localVersion.releaseDate);
      
      if (serverDate > localDate) {
        console.log('[VERSION] Scripts update available:', {
          local: localVersion.version,
          server: serverVersion.version
        });
        return { 
          needed: true, 
          version: serverVersion.version,
          size: await this.getFileSize(serverVersion.downloadUrl)
        };
      }

      console.log('[VERSION] Scripts are up to date');
      return { needed: false };
    } catch (error) {
      console.error('[VERSION] Error checking scripts update:', error);
      return { needed: false };
    }
  }

  /**
   * Fetch version information from server
   */
  async fetchVersionInfo(type: 'scripts' | 'runtime'): Promise<VersionInfo | null> {
    try {
      const url = this.UPDATE_URLS[type];
      console.log(`[VERSION] Fetching ${type} version from ${url}`);

      const response = await axios.get(url, {
        timeout: 30000,
        headers: { 'User-Agent': 'OneWhispr-App/1.0.0' }
      });

      return response.data as VersionInfo;
    } catch (error) {
      console.error(`[VERSION] Error fetching ${type} version:`, error);
      return null;
    }
  }

  /**
   * Get file size from URL
   */
  private async getFileSize(url: string): Promise<number> {
    try {
      const response = await axios.head(url, {
        timeout: 10000,
        headers: { 'User-Agent': 'OneWhispr-App/1.0.0' }
      });
      
      const contentLength = response.headers['content-length'];
      return contentLength ? parseInt(contentLength, 10) : 0;
    } catch (error) {
      console.error('[VERSION] Error getting file size:', error);
      return 0;
    }
  }

  /**
   * Check for local 7z files (for first-time extraction)
   */
  checkForLocal7zFiles(): { hasRuntimeZip: boolean; hasScriptsZip: boolean } {
    const runtimeZipPath = path.join(this.backendPath, 'OneWhispr-Runtime-Base.7z');
    const scriptsZipPath = path.join(this.backendPath, 'OneWhispr-Scripts.7z');

    return {
      hasRuntimeZip: fs.existsSync(runtimeZipPath),
      hasScriptsZip: fs.existsSync(scriptsZipPath)
    };
  }

  /**
   * Check if backend is already extracted and ready
   */
  isBackendReady(): boolean {
    const exePath = path.join(this.backendPath, 'One Whispr Backend.exe');
    const scriptsPath = path.join(this.backendPath, 'scripts');
    
    return fs.existsSync(exePath) && fs.existsSync(scriptsPath);
  }
}
