import { app, Tray, Menu, nativeImage, MenuItemConstructorOptions, nativeTheme } from 'electron';
import path from 'path';
import { getSettingsWindow, createSettingsWindow } from './windows/settings';
import { disposeFeatures } from './features';
import { IS_MICROSOFT } from './constants';

let tray: Tray | null = null;
let mlLibrariesReady: boolean = false;

// Create a function to update the tray context menu
function updateTrayMenu() {
  if (!tray) return;
  
  // Build the menu items including the dynamic modes submenu
  const menuItems: MenuItemConstructorOptions[] = [
    {
      label: 'Start/Stop Recording',
    },
    {
      label: 'Transcribe File'
    },
    { type: 'separator' },
    {
      label: 'Switch Modes'
    },
    {
      label: 'Open Settings',
      click: () => {
        let window = getSettingsWindow();
        if (!window) {
          window = createSettingsWindow();
        }
        window.show();
        window.focus();
      }
    },
    { type: 'separator' },
    {
      label: 'Quit',
      click: async () => {
        // Set the quitting flag first
        (app as any).isQuitting = true;
        
        try {
          console.log('Graceful shutdown initiated from tray menu');
          
          // Close all windows first - settings window and any others
          const settingsWindow = getSettingsWindow();
          if (settingsWindow) {
            settingsWindow.close();
          }
          
          // Give a moment for windows to close
          await new Promise(resolve => setTimeout(resolve, 500));
          
          // Dispose features (includes Python backend cleanup)
          await disposeFeatures();
          
          // Finally quit the app
          app.quit();
        } catch (error) {
          console.error('Error during tray quit:', error);
          // Force quit if there was an error
          app.exit(0);
        }
      }
    }
  ];

  const contextMenu = Menu.buildFromTemplate(menuItems);
  tray.setContextMenu(contextMenu);
}

// Function to update tray icon based on theme
function updateTrayIcon() {
  if (!tray) return;
  
  const isDarkMode = nativeTheme.shouldUseDarkColors;
  const iconFileName = isDarkMode ? 'one.whispr-white.png' : 'one.whispr-black.png';
  
  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, iconFileName)
    : path.join(process.cwd(), 'src', 'assets', iconFileName);

  const icon = nativeImage.createFromPath(iconPath);
  tray.setImage(icon);
}

export function setupTray(): void {
  // Use white icon by default (will be updated immediately by updateTrayIcon)
  const iconFileName = 'one.whispr-white.png';
  
  const iconPath = app.isPackaged
    ? path.join(process.resourcesPath, iconFileName)
    : path.join(process.cwd(), 'src', 'assets', iconFileName);

  const icon = nativeImage.createFromPath(iconPath);
  
  tray = new Tray(icon);
  tray.setToolTip('Whispr');
  
  // Create initial menu
  updateTrayMenu();
  
  // Update the icon based on current theme
  updateTrayIcon();
  
  // Listen for theme changes
  nativeTheme.on('updated', updateTrayIcon);

  // Handle double-click on tray icon
  tray.on('double-click', () => {
    let window = getSettingsWindow();
    if (!window) {
      window = createSettingsWindow();
    }
    window.show();
    window.focus();
  });
}

export function getTray(): Tray | null {
  return tray;
}

// Add a way to refresh the tray menu when modes change
export function refreshTrayMenu(): void {
  updateTrayMenu();
}
