import { ipc<PERSON><PERSON><PERSON> } from 'electron';

/**
 * Launcher-specific IPC methods for one.whispr-setup
 */
export const launcherAPI = {
  // Check launch readiness
  checkLaunchReady: () => ipcRenderer.invoke('check-launch-ready'),
  
  // Download methods
  startDownload: () => ipcRenderer.invoke('download:start'),
  downloadBackend: () => ipcRenderer.invoke('backend:download'),
  
  // Launch main app
  launchMainApp: () => ipcRenderer.invoke('launch-main-app'),
  
  // Get environment variables
  getEnv: () => ipcRenderer.invoke('get-env'),
  
  // Exit launcher
  exit: () => ipcRenderer.invoke('exit'),

  // Event listeners
  onDownloadProgress: (callback: (progress: any) => void) => {
    const subscription = (_event: any, progress: any) => callback(progress);
    ipcRenderer.on('download:progress', subscription);
    
    return () => {
      ipcRenderer.removeListener('download:progress', subscription);
    };
  },

  onDownloadComplete: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('download:complete', subscription);
    
    return () => {
      ipcRenderer.removeListener('download:complete', subscription);
    };
  },

  onDownloadError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('download:error', subscription);
    
    return () => {
      ipcRenderer.removeListener('download:error', subscription);
    };
  },

  onBackendProgress: (callback: (progress: any) => void) => {
    const subscription = (_event: any, progress: any) => callback(progress);
    ipcRenderer.on('backend:progress', subscription);
    
    return () => {
      ipcRenderer.removeListener('backend:progress', subscription);
    };
  },

  onBackendComplete: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('backend:complete', subscription);
    
    return () => {
      ipcRenderer.removeListener('backend:complete', subscription);
    };
  },

  onBackendError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('backend:error', subscription);
    
    return () => {
      ipcRenderer.removeListener('backend:error', subscription);
    };
  },

  onMainAppError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('main-app:error', subscription);
    
    return () => {
      ipcRenderer.removeListener('main-app:error', subscription);
    };
  },

  onMainAppReady: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('main-app:ready', subscription);
    
    return () => {
      ipcRenderer.removeListener('main-app:ready', subscription);
    };
  }
};
