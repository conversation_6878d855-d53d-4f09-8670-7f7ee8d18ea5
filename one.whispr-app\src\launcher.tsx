import React from 'react';
import ReactDOM from 'react-dom/client';
import './main.css';
import { LauncherProvider } from './features/launcher/context';
import { LauncherPage } from './features/launcher/page';

// Initialize the launcher application
const rootElement = document.getElementById("root");
if (rootElement) {
  const root = ReactDOM.createRoot(rootElement);
  root.render(
    <React.StrictMode>
      <LauncherProvider>
        <LauncherPage />
      </LauncherProvider>
    </React.StrictMode>
  );
}
