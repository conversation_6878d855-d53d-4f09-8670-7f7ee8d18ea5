import ElectronStore from 'electron-store';

export interface WindowState {
  width?: number;
  height?: number;
  x?: number;
  y?: number;
  isMaximised?: boolean;
  hasBeenSaved?: boolean;
  displayId?: number;
  displayScaleFactor?: number;
}

interface StoreSchema {
  windowStates: {
    [key: string]: WindowState;
  };
}

const store = new ElectronStore<StoreSchema>({
  defaults: {
    windowStates: {}
  },
  name: 'window-state'
});

export function getWindowState(windowName: string): WindowState {
  const states = store.get('windowStates');
  return states[windowName] || {};
}

export function setWindowState(windowName: string, state: WindowState): void {
  const states = store.get('windowStates');
  store.set('windowStates', { ...states, [windowName]: state });
}

export function resetStore(): void {
  store.clear();
  store.set('windowStates', {});
}

import { getSettingsWindow, createSettingsWindow, showSettingsWindow } from './settings';

export { getSettingsWindow, createSettingsWindow, showSettingsWindow }