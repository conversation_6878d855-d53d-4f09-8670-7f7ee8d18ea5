import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Define types for better type safety
interface AuthCallbackData {
  token: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
}

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatarUrl?: string;
  provider: 'email' | 'google' | 'twitter';
  providerId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserSession {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt: string;
  scopes?: string[];
  deviceInfo?: {
    platform: string;
    userAgent?: string;
    deviceId: string;
  };
  isActive: boolean;
  lastAccessedAt: string;
  createdAt: string;
}

interface AuthSettings {
  id: string;
  userId: string;
  autoLogin: boolean;
  rememberDevice: boolean;
  sessionTimeout: number;
  biometricEnabled: boolean;
  lastSyncedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthStatus {
  isAuthenticated: boolean;
  user?: User;
  session?: UserSession;
}

interface AuthSuccessData {
  user: User;
  session: UserSession;
  token: string;
}

interface AuthErrorData {
  error: string;
}

interface AuthStateChangeData {
  isAuthenticated: boolean;
  user?: User;
}

interface BackendStatusData {
  pythonRunning: boolean;
  pythonConnected: boolean;
  port: number | null;
}

// Expose protected methods that allow the renderer process to use IPC
contextBridge.exposeInMainWorld('electron', {
  // Authentication flow methods
  openExternalUrl: (url: string) => ipcRenderer.invoke('open-external-url', url),
  getAuthUrls: () => ipcRenderer.invoke('get-auth-urls'),
  
  // === AUTHENTICATION IPC METHODS ===
  
  // Get current authentication status
  getAuthStatus: () => ipcRenderer.invoke('auth:getStatus') as Promise<AuthStatus>,
  
  // Get current user's access token
  getAccessToken: () => ipcRenderer.invoke('auth:getAccessToken') as Promise<string | null>,
  
  // Logout current user
  logout: () => ipcRenderer.invoke('auth:logout') as Promise<boolean>,
  
  // Update user activity (extend session)
  updateActivity: () => ipcRenderer.invoke('auth:updateActivity') as Promise<boolean>,
  
  // Get current user
  getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser') as Promise<User | null>,
  
  // Get user settings
  getUserSettings: (userId?: string) => ipcRenderer.invoke('auth:getUserSettings', userId) as Promise<AuthSettings | null>,
  
  // Process authentication callback (for manual triggering)
  processAuthCallback: (callbackData: AuthCallbackData) => ipcRenderer.send('auth:processCallback', callbackData),
  
  // === AUTHENTICATION EVENT LISTENERS ===
  
  // Authentication callback listener (OAuth callback URL processed)
  onAuthCallback: (callback: (data: AuthCallbackData) => void) => {
    const subscription = (_event: any, data: AuthCallbackData) => callback(data);
    ipcRenderer.on('auth-callback', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-callback', subscription);
    };
  },
  
  // Authentication success listener
  onAuthSuccess: (callback: (data: AuthSuccessData) => void) => {
    const subscription = (_event: any, data: AuthSuccessData) => callback(data);
    ipcRenderer.on('auth-success', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-success', subscription);
    };
  },
  
  // Authentication error listener
  onAuthError: (callback: (data: AuthErrorData) => void) => {
    const subscription = (_event: any, data: AuthErrorData) => callback(data);
    ipcRenderer.on('auth-error', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-error', subscription);
    };
  },
  
  // Authentication state change listener
  onAuthStateChanged: (callback: (data: AuthStateChangeData) => void) => {
    const subscription = (_event: any, data: AuthStateChangeData) => callback(data);
    ipcRenderer.on('auth-state-changed', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('auth-state-changed', subscription);
    };
  },
  
  // === DATABASE IPC (for settings, etc.) ===
  
  // Settings database methods
  getSettings: () => ipcRenderer.invoke('settings:get'),
  updateSettings: (updates: any) => ipcRenderer.invoke('settings:update', updates),
  
  // === BACKEND COMMUNICATION ===
  
  // Backend communication
  sendCommand: (type: string, args?: any) => ipcRenderer.invoke('send-command', type, args),
  getBackendStatus: () => ipcRenderer.invoke('python-get-status'),
  reconnectBackend: () => ipcRenderer.invoke('python-reconnect'),
  
  // Backend WebSocket message listener
  onBackendMessage: (callback: (message: any) => void) => {
    const subscription = (_event: any, message: any) => callback(message);
    ipcRenderer.on('python-ws-message', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-message', subscription);
    };
  },
  
  // Backend connection status listeners
  onBackendConnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connected', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-connected', subscription);
    };
  },

  onBackendConnectionFailed: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connection-failed', subscription);
    
    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-ws-connection-failed', subscription);
    };
  },

  // Backend server welcome listener
  onBackendServerWelcome: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('python-server-welcome', subscription);

    // Return cleanup function
    return () => {
      ipcRenderer.removeListener('python-server-welcome', subscription);
    };
  },

  // === LAUNCHER IPC METHODS (for Microsoft Store builds) ===

  // Check if launcher is ready
  checkLauncherReady: () => ipcRenderer.invoke('launcher:check-ready'),

  // Check for updates
  checkLauncherUpdates: () => ipcRenderer.invoke('launcher:check-updates'),

  // Download updates
  downloadLauncherUpdates: () => ipcRenderer.invoke('launcher:download-updates'),

  // Extract updates
  extractLauncherUpdates: () => ipcRenderer.invoke('launcher:extract-updates'),

  // Launch main app
  launchMainApp: () => ipcRenderer.invoke('launcher:launch-main-app'),

  // Get launcher environment
  getLauncherEnv: () => ipcRenderer.invoke('launcher:get-env'),

  // Exit launcher
  exitLauncher: () => ipcRenderer.invoke('launcher:exit'),

  // Launcher event listeners
  onLauncherProgress: (callback: (progress: any) => void) => {
    const subscription = (_event: any, progress: any) => callback(progress);
    ipcRenderer.on('launcher:progress', subscription);

    return () => {
      ipcRenderer.removeListener('launcher:progress', subscription);
    };
  },

  onLauncherMainAppError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('launcher:main-app-error', subscription);

    return () => {
      ipcRenderer.removeListener('launcher:main-app-error', subscription);
    };
  }
});

// Export types for TypeScript support
export type { 
  AuthCallbackData, 
  User, 
  UserSession, 
  AuthSettings, 
  AuthStatus, 
  AuthSuccessData, 
  AuthErrorData, 
  AuthStateChangeData, 
  BackendStatusData 
};
