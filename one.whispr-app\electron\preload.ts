import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron';

// Auth-related types
interface AuthCallbackData {
  token: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
}

interface User {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  fullName?: string;
  avatarUrl?: string;
  provider: 'email' | 'google' | 'twitter';
  providerId?: string;
  isActive: boolean;
  lastLoginAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface UserSession {
  id: string;
  userId: string;
  accessToken: string;
  refreshToken?: string;
  tokenType: string;
  expiresAt: string;
  scopes?: string[];
  deviceInfo?: {
    platform: string;
    userAgent?: string;
    deviceId: string;
  };
  isActive: boolean;
  lastAccessedAt: string;
  createdAt: string;
}

interface AuthSettings {
  id: string;
  userId: string;
  autoLogin: boolean;
  rememberDevice: boolean;
  sessionTimeout: number;
  twoFactorEnabled: boolean;
  biometricEnabled: boolean;
  lastSyncedAt?: string;
  createdAt: string;
  updatedAt: string;
}

interface AuthStatus {
  isAuthenticated: boolean;
  user?: User;
  session?: UserSession;
}

interface AuthSuccessData {
  user: User;
  session: UserSession;
  token: string;
}

interface AuthErrorData {
  error: string;
}

interface AuthStateChangeData {
  isAuthenticated: boolean;
  user?: User;
}

interface BackendStatusData {
  pythonRunning: boolean;
  pythonConnected: boolean;
  port: number | null;
}

/**
 * Preload script for one.whispr-app
 * Exposes safe IPC methods to the renderer process
 */
contextBridge.exposeInMainWorld('electron', {
  // === CORE IPC METHODS ===
  ipcRenderer: {
    send: (channel: string, ...args: any[]) => {
      ipcRenderer.send(channel, ...args);
    },
    
    invoke: (channel: string, ...args: any[]): Promise<any> => {
      return ipcRenderer.invoke(channel, ...args);
    },
    
    on: (channel: string, listener: (...args: any[]) => void) => {
      ipcRenderer.on(channel, (_event, ...args) => listener(...args));
      
      return () => {
        ipcRenderer.removeListener(channel, (_event, ...args) => listener(...args));
      };
    },
    
    removeAllListeners: (channel: string) => {
      ipcRenderer.removeAllListeners(channel);
    },
    
    once: (channel: string, listener: (...args: any[]) => void) => {
      ipcRenderer.once(channel, (_event, ...args) => listener(...args));
    }
  },

  // === AUTHENTICATION METHODS ===
  openExternalUrl: (url: string) => ipcRenderer.invoke('open-external-url', url),
  getAuthUrls: () => ipcRenderer.invoke('get-auth-urls'),
  getAuthStatus: () => ipcRenderer.invoke('auth:getStatus') as Promise<AuthStatus>,
  getAccessToken: () => ipcRenderer.invoke('auth:getAccessToken') as Promise<string | null>,
  logout: () => ipcRenderer.invoke('auth:logout') as Promise<boolean>,
  updateActivity: () => ipcRenderer.invoke('auth:updateActivity') as Promise<boolean>,
  getCurrentUser: () => ipcRenderer.invoke('auth:getCurrentUser') as Promise<User | null>,
  getUserSettings: (userId?: string) => ipcRenderer.invoke('auth:getUserSettings', userId) as Promise<AuthSettings | null>,
  processAuthCallback: (callbackData: AuthCallbackData) => ipcRenderer.send('auth:processCallback', callbackData),

  // Authentication event listeners
  onAuthCallback: (callback: (data: AuthCallbackData) => void) => {
    const subscription = (_event: any, data: AuthCallbackData) => callback(data);
    ipcRenderer.on('auth-callback', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-callback', subscription);
    };
  },

  onAuthSuccess: (callback: (data: AuthSuccessData) => void) => {
    const subscription = (_event: any, data: AuthSuccessData) => callback(data);
    ipcRenderer.on('auth-success', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-success', subscription);
    };
  },

  onAuthError: (callback: (data: AuthErrorData) => void) => {
    const subscription = (_event: any, data: AuthErrorData) => callback(data);
    ipcRenderer.on('auth-error', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-error', subscription);
    };
  },

  onAuthStateChange: (callback: (data: AuthStateChangeData) => void) => {
    const subscription = (_event: any, data: AuthStateChangeData) => callback(data);
    ipcRenderer.on('auth-state-change', subscription);
    
    return () => {
      ipcRenderer.removeListener('auth-state-change', subscription);
    };
  },

  // === BACKEND METHODS ===
  getBackendStatus: () => ipcRenderer.invoke('backend:getStatus') as Promise<BackendStatusData>,
  startBackend: () => ipcRenderer.invoke('backend:start') as Promise<boolean>,
  stopBackend: () => ipcRenderer.invoke('backend:stop') as Promise<boolean>,
  restartBackend: () => ipcRenderer.invoke('backend:restart') as Promise<boolean>,
  sendBackendRequest: (endpoint: string, data?: any) => ipcRenderer.invoke('backend:request', endpoint, data),

  onBackendStatusChange: (callback: (data: BackendStatusData) => void) => {
    const subscription = (_event: any, data: BackendStatusData) => callback(data);
    ipcRenderer.on('backend-status-change', subscription);
    
    return () => {
      ipcRenderer.removeListener('backend-status-change', subscription);
    };
  },

  onBackendServerWelcome: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('python-server-welcome', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-server-welcome', subscription);
    };
  },

  // === APP METHODS ===
  getSettings: () => ipcRenderer.invoke('settings:get'),
  updateSettings: (updates: any) => ipcRenderer.invoke('settings:update', updates),
  sendCommand: (type: string, args?: any) => ipcRenderer.invoke('send-command', type, args),
  reconnectBackend: () => ipcRenderer.invoke('python-reconnect'),

  onBackendMessage: (callback: (message: any) => void) => {
    const subscription = (_event: any, message: any) => callback(message);
    ipcRenderer.on('python-ws-message', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-message', subscription);
    };
  },
  
  onBackendConnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connected', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-connected', subscription);
    };
  },

  onBackendConnectionFailed: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-connection-failed', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-ws-connection-failed', subscription);
    };
  },

  onBackendDisconnected: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('python-ws-disconnected', subscription);

    return () => {
      ipcRenderer.removeListener('python-ws-disconnected', subscription);
    };
  },

  // === LAUNCHER METHODS (Microsoft Store builds) ===
  checkLauncherReady: () => ipcRenderer.invoke('launcher:check-ready'),
  checkLauncherUpdates: () => ipcRenderer.invoke('launcher:check-updates'),
  downloadLauncherUpdates: () => ipcRenderer.invoke('launcher:download-updates'),
  extractLauncherUpdates: () => ipcRenderer.invoke('launcher:extract-updates'),
  launchMainApp: () => ipcRenderer.invoke('launcher:launch-main-app'),
  getLauncherEnv: () => ipcRenderer.invoke('launcher:get-env'),
  exitLauncher: () => ipcRenderer.invoke('launcher:exit'),

  // Download methods (for useSetup compatibility)
  startDownload: () => ipcRenderer.invoke('download:start'),
  downloadBackend: () => ipcRenderer.invoke('backend:download'),

  // Event listeners for downloads
  onDownloadProgress: (callback: (progress: any) => void) => {
    const subscription = (_event: any, progress: any) => callback(progress);
    ipcRenderer.on('download:progress', subscription);

    return () => {
      ipcRenderer.removeListener('download:progress', subscription);
    };
  },

  onDownloadComplete: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('download:complete', subscription);

    return () => {
      ipcRenderer.removeListener('download:complete', subscription);
    };
  },

  onDownloadError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('download:error', subscription);

    return () => {
      ipcRenderer.removeListener('download:error', subscription);
    };
  },

  onBackendProgress: (callback: (progress: any) => void) => {
    const subscription = (_event: any, progress: any) => callback(progress);
    ipcRenderer.on('backend:progress', subscription);

    return () => {
      ipcRenderer.removeListener('backend:progress', subscription);
    };
  },

  onBackendComplete: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('backend:complete', subscription);

    return () => {
      ipcRenderer.removeListener('backend:complete', subscription);
    };
  },

  onBackendError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('backend:error', subscription);

    return () => {
      ipcRenderer.removeListener('backend:error', subscription);
    };
  },

  onMainAppError: (callback: (error: any) => void) => {
    const subscription = (_event: any, error: any) => callback(error);
    ipcRenderer.on('main-app:error', subscription);

    return () => {
      ipcRenderer.removeListener('main-app:error', subscription);
    };
  },

  onMainAppReady: (callback: () => void) => {
    const subscription = () => callback();
    ipcRenderer.on('main-app:ready', subscription);

    return () => {
      ipcRenderer.removeListener('main-app:ready', subscription);
    };
  }
});

// Export types for TypeScript support
export type {
  AuthCallbackData,
  User,
  UserSession,
  AuthSettings,
  AuthStatus,
  AuthSuccessData,
  AuthErrorData,
  AuthStateChangeData,
  BackendStatusData
};
