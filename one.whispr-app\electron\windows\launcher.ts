import { BrowserWindow, app, screen } from 'electron';
import path from 'path';
import { IS_DEV } from '../constants';

let launcherWindow: BrowserWindow | null = null;

/**
 * Create the launcher window for Microsoft Store builds
 */
export function createLauncherWindow(): BrowserWindow {
  console.log('[LAUNCHER] Creating launcher window...');

  // Get primary display dimensions
  const primaryDisplay = screen.getPrimaryDisplay();
  const { width: screenWidth, height: screenHeight } = primaryDisplay.workAreaSize;

  // Calculate centered position
  const windowWidth = 480;
  const windowHeight = 600;
  const x = Math.round((screenWidth - windowWidth) / 2);
  const y = Math.round((screenHeight - windowHeight) / 2);

  launcherWindow = new BrowserWindow({
    width: windowWidth,
    height: windowHeight,
    x,
    y,
    minWidth: 400,
    minHeight: 500,
    maxWidth: 600,
    maxHeight: 800,
    resizable: false,
    maximizable: false,
    minimizable: false,
    alwaysOnTop: true,
    frame: false,
    transparent: false,
    backgroundColor: '#1a1a1a',
    show: false, // Don't show immediately
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, '../preload.js'),
      webSecurity: true,
      allowRunningInsecureContent: false,
      experimentalFeatures: false
    },
    icon: app.isPackaged
      ? path.join(process.resourcesPath, 'icon.ico')
      : path.join(process.cwd(), 'src', 'assets', 'icon.ico')
  });

  // Load the launcher page
  if (IS_DEV) {
    console.log('[LAUNCHER] Loading from dev server...');
    launcherWindow.loadURL('http://localhost:5173/launcher.html')
      .catch(err => {
        console.error('[LAUNCHER] Failed to load from dev server:', err);
        // Fallback to built files if dev server fails
        console.log('[LAUNCHER] Falling back to built files');
        const htmlPath = path.join(__dirname, '../../../renderer/launcher.html');
        if (require('fs').existsSync(htmlPath) && launcherWindow) {
          launcherWindow.loadFile(htmlPath);
        }
      });
  } else {
    console.log('[LAUNCHER] Running in production mode, loading from built files');
    const htmlPath = path.join(__dirname, '../../../renderer/launcher.html');
    
    if (!require('fs').existsSync(htmlPath)) {
      console.error('[LAUNCHER] Could not find HTML file at:', htmlPath);
      throw new Error(`HTML file not found at ${htmlPath}`);
    }
    
    launcherWindow.loadFile(htmlPath);
  }

  // Handle window events
  launcherWindow.on('closed', () => {
    console.log('[LAUNCHER] Window closed');
    launcherWindow = null;
  });

  launcherWindow.on('ready-to-show', () => {
    console.log('[LAUNCHER] Window ready to show');
  });

  // Prevent navigation
  launcherWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    
    // Allow navigation to localhost in development
    if (IS_DEV && parsedUrl.hostname === 'localhost') {
      return;
    }
    
    // Prevent all other navigation
    event.preventDefault();
  });

  // Handle new window requests
  launcherWindow.webContents.setWindowOpenHandler(() => {
    return { action: 'deny' };
  });

  console.log('[LAUNCHER] Launcher window created');
  return launcherWindow;
}

/**
 * Get the launcher window instance
 */
export function getLauncherWindow(): BrowserWindow | null {
  return launcherWindow;
}

/**
 * Show the launcher window
 */
export function showLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Showing launcher window');
    launcherWindow.show();
    launcherWindow.focus();
  } else {
    console.warn('[LAUNCHER] Cannot show window - not created yet');
  }
}

/**
 * Hide the launcher window
 */
export function hideLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Hiding launcher window');
    launcherWindow.hide();
  }
}

/**
 * Close the launcher window
 */
export function closeLauncherWindow(): void {
  if (launcherWindow) {
    console.log('[LAUNCHER] Closing launcher window');
    launcherWindow.close();
    launcherWindow = null;
  }
}

/**
 * Check if launcher window exists and is visible
 */
export function isLauncherWindowVisible(): boolean {
  return launcherWindow !== null && launcherWindow.isVisible();
}
