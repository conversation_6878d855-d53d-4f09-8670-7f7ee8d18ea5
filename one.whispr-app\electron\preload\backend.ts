import { ipc<PERSON><PERSON><PERSON> } from 'electron';

// Backend-related types
export interface BackendStatusData {
  pythonRunning: boolean;
  pythonConnected: boolean;
  port: number | null;
}

/**
 * Backend/Python-related IPC methods
 */
export const backendAPI = {
  // Backend status and control
  getBackendStatus: () => ipcRenderer.invoke('backend:getStatus') as Promise<BackendStatusData>,
  startBackend: () => ipcRenderer.invoke('backend:start') as Promise<boolean>,
  stopBackend: () => ipcRenderer.invoke('backend:stop') as Promise<boolean>,
  restartBackend: () => ipcRenderer.invoke('backend:restart') as Promise<boolean>,
  
  // Backend communication
  sendBackendRequest: (endpoint: string, data?: any) => ipcRenderer.invoke('backend:request', endpoint, data),
  
  // Event listeners
  onBackendStatusChange: (callback: (data: BackendStatusData) => void) => {
    const subscription = (_event: any, data: BackendStatusData) => callback(data);
    ipcRenderer.on('backend-status-change', subscription);
    
    return () => {
      ipcRenderer.removeListener('backend-status-change', subscription);
    };
  },

  onBackendServerWelcome: (callback: (data: any) => void) => {
    const subscription = (_event: any, data: any) => callback(data);
    ipcRenderer.on('python-server-welcome', subscription);
    
    return () => {
      ipcRenderer.removeListener('python-server-welcome', subscription);
    };
  }
};
