/**
 * Types for the launcher feature
 */

export interface LauncherState {
  currentPhase: 'initializing' | 'checking' | 'downloading' | 'extracting' | 'starting' | 'waiting' | 'error' | 'complete';
  progress?: DownloadProgress;
  error?: string;
  mainAppError?: string;
  isReady: boolean;
  needsUpdate: boolean;
  updateType?: 'scripts' | 'runtime' | 'both';
}

export interface DownloadProgress {
  percentage: number;
  downloadedBytes: number;
  totalBytes: number;
  downloadSpeed: number;
  phase: string;
  currentFile?: string;
}

export interface UpdateInfo {
  hasUpdate: boolean;
  updateType: 'scripts' | 'runtime' | 'both';
  scriptsVersion?: string;
  runtimeVersion?: string;
  scriptsSize?: number;
  runtimeSize?: number;
}

export interface LauncherContextType {
  state: LauncherState;
  checkForUpdates: () => Promise<boolean>;
  downloadUpdates: () => Promise<boolean>;
  extractUpdates: () => Promise<boolean>;
  launchMainApp: () => Promise<boolean>;
  exitLauncher: () => Promise<void>;
}
